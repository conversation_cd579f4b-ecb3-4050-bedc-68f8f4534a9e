{"openapi": "3.0.1", "info": {"title": "MyntraClone.API", "version": "1.0"}, "paths": {"/api/Auth/register": {"post": {"tags": ["<PERSON><PERSON>"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RegisterDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RegisterDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/RegisterDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Auth/login": {"post": {"tags": ["<PERSON><PERSON>"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/LoginDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/LoginDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/LoginDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Cart": {"get": {"tags": ["<PERSON><PERSON>"], "responses": {"200": {"description": "OK"}}}, "post": {"tags": ["<PERSON><PERSON>"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateCartItemDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateCartItemDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateCartItemDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Cart/items/{itemId}": {"delete": {"tags": ["<PERSON><PERSON>"], "parameters": [{"name": "itemId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Cart/clear": {"delete": {"tags": ["<PERSON><PERSON>"], "responses": {"200": {"description": "OK"}}}}, "/api/Categories": {"get": {"tags": ["Categories"], "parameters": [{"name": "name", "in": "query", "schema": {"type": "string"}}, {"name": "page", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 1}}, {"name": "pageSize", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 10}}], "responses": {"200": {"description": "OK"}}}, "post": {"tags": ["Categories"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateCategoryDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateCategoryDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateCategoryDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Categories/{id}": {"get": {"tags": ["Categories"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}, "put": {"tags": ["Categories"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateCategoryDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateCategoryDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateCategoryDto"}}}}, "responses": {"200": {"description": "OK"}}}, "delete": {"tags": ["Categories"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Orders": {"post": {"tags": ["Orders"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateOrderDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateOrderDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateOrderDto"}}}}, "responses": {"200": {"description": "OK"}}}, "get": {"tags": ["Orders"], "responses": {"200": {"description": "OK"}}}}, "/api/Orders/user/{userId}": {"get": {"tags": ["Orders"], "parameters": [{"name": "userId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Orders/{orderId}": {"get": {"tags": ["Orders"], "parameters": [{"name": "orderId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Products": {"get": {"tags": ["Products"], "parameters": [{"name": "name", "in": "query", "schema": {"type": "string"}}, {"name": "categoryId", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "page", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 1}}, {"name": "pageSize", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 10}}], "responses": {"200": {"description": "OK"}}}, "post": {"tags": ["Products"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateProductDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateProductDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateProductDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Products/{id}": {"get": {"tags": ["Products"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}, "put": {"tags": ["Products"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateProductDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateProductDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateProductDto"}}}}, "responses": {"200": {"description": "OK"}}}, "delete": {"tags": ["Products"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Products/upload": {"post": {"tags": ["Products"], "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"file": {"type": "string", "format": "binary"}}}, "encoding": {"file": {"style": "form"}}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Users": {"get": {"tags": ["Users"], "responses": {"200": {"description": "OK"}}}, "post": {"tags": ["Users"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateUserDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateUserDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateUserDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Users/<USER>": {"get": {"tags": ["Users"], "responses": {"200": {"description": "OK"}}}}, "/api/Wishlist/{userId}": {"get": {"tags": ["Wishlist"], "parameters": [{"name": "userId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Wishlist/items": {"post": {"tags": ["Wishlist"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AddWishlistItemDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AddWishlistItemDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/AddWishlistItemDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Wishlist/items/{itemId}": {"delete": {"tags": ["Wishlist"], "parameters": [{"name": "itemId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}}, "components": {"schemas": {"AddWishlistItemDto": {"required": ["productId"], "type": "object", "properties": {"productId": {"type": "integer", "format": "int32"}, "quantity": {"maximum": 2147483647, "minimum": 1, "type": "integer", "format": "int32"}}, "additionalProperties": false}, "CreateCartItemDto": {"required": ["productId"], "type": "object", "properties": {"productId": {"type": "integer", "format": "int32"}, "quantity": {"maximum": 2147483647, "minimum": 1, "type": "integer", "format": "int32"}}, "additionalProperties": false}, "CreateCategoryDto": {"required": ["description", "name"], "type": "object", "properties": {"name": {"maxLength": 100, "minLength": 2, "type": "string"}, "description": {"maxLength": 300, "minLength": 0, "type": "string"}}, "additionalProperties": false}, "CreateOrderDto": {"required": ["items", "paymentIntentId", "paymentReference", "shippingAddress", "userId"], "type": "object", "properties": {"userId": {"type": "integer", "format": "int32"}, "items": {"minItems": 1, "type": "array", "items": {"$ref": "#/components/schemas/CreateOrderItemDto"}}, "shippingAddress": {"minLength": 5, "type": "string"}, "paymentReference": {"minLength": 1, "type": "string"}, "paymentIntentId": {"minLength": 1, "type": "string"}}, "additionalProperties": false}, "CreateOrderItemDto": {"required": ["productId"], "type": "object", "properties": {"productId": {"type": "integer", "format": "int32"}, "quantity": {"maximum": 2147483647, "minimum": 1, "type": "integer", "format": "int32"}, "price": {"minimum": 0.01, "type": "number", "format": "double"}}, "additionalProperties": false}, "CreateProductDto": {"required": ["brand", "categoryId", "description", "name"], "type": "object", "properties": {"name": {"maxLength": 100, "minLength": 0, "type": "string"}, "description": {"minLength": 1, "type": "string"}, "brand": {"minLength": 1, "type": "string"}, "price": {"minimum": 0.01, "type": "number", "format": "double"}, "discountPrice": {"minimum": 0, "type": "number", "format": "double"}, "stock": {"maximum": 2147483647, "minimum": 0, "type": "integer", "format": "int32"}, "categoryId": {"type": "integer", "format": "int32"}, "imageUrl": {"type": "string", "format": "uri", "nullable": true}}, "additionalProperties": false}, "CreateUserDto": {"required": ["email", "name", "password"], "type": "object", "properties": {"name": {"minLength": 2, "type": "string"}, "email": {"minLength": 1, "type": "string", "format": "email"}, "password": {"minLength": 6, "type": "string"}}, "additionalProperties": false}, "LoginDto": {"required": ["email", "password"], "type": "object", "properties": {"email": {"minLength": 1, "type": "string", "format": "email"}, "password": {"minLength": 1, "type": "string"}}, "additionalProperties": false}, "RegisterDto": {"required": ["email", "name", "password"], "type": "object", "properties": {"name": {"minLength": 2, "type": "string"}, "email": {"minLength": 1, "type": "string", "format": "email"}, "password": {"minLength": 6, "type": "string"}}, "additionalProperties": false}, "UpdateUserDto": {"required": ["email", "name", "role"], "type": "object", "properties": {"name": {"minLength": 2, "type": "string"}, "email": {"minLength": 1, "type": "string", "format": "email"}, "role": {"minLength": 1, "pattern": "Admin|Customer", "type": "string"}}, "additionalProperties": false}}, "securitySchemes": {"Bearer": {"type": "<PERSON><PERSON><PERSON><PERSON>", "description": "JWT Authorization header using the <PERSON><PERSON> scheme. Example: 'Bearer {your token}'", "name": "Authorization", "in": "header"}}}, "security": [{"Bearer": []}]}