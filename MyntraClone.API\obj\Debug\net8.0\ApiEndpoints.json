[{"ContainingType": "MyntraClone.API.Controllers.AuthController", "Method": "<PERSON><PERSON>", "RelativePath": "api/Auth/login", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "dto", "Type": "MyntraClone.API.DTOs.Auth.LoginDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "MyntraClone.API.Controllers.AuthController", "Method": "Register", "RelativePath": "api/Auth/register", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "dto", "Type": "MyntraClone.API.DTOs.Auth.RegisterDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "MyntraClone.API.Controllers.CartController", "Method": "GetCart", "RelativePath": "api/Cart", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "MyntraClone.API.Controllers.CartController", "Method": "AddItem", "RelativePath": "api/Cart", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "dto", "Type": "MyntraClone.API.DTOs.CreateCartItemDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "MyntraClone.API.Controllers.CartController", "Method": "ClearCart", "RelativePath": "api/Cart/clear", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "MyntraClone.API.Controllers.CartController", "Method": "RemoveItem", "RelativePath": "api/Cart/items/{itemId}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "itemId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "MyntraClone.API.Controllers.CategoriesController", "Method": "GetAll", "RelativePath": "api/Categories", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "name", "Type": "System.String", "IsRequired": false}, {"Name": "page", "Type": "System.Int32", "IsRequired": false}, {"Name": "pageSize", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "MyntraClone.API.Controllers.CategoriesController", "Method": "Create", "RelativePath": "api/Categories", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "dto", "Type": "MyntraClone.API.DTOs.CreateCategoryDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "MyntraClone.API.Controllers.CategoriesController", "Method": "GetById", "RelativePath": "api/Categories/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "MyntraClone.API.Controllers.CategoriesController", "Method": "Update", "RelativePath": "api/Categories/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "dto", "Type": "MyntraClone.API.DTOs.CreateCategoryDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "MyntraClone.API.Controllers.CategoriesController", "Method": "Delete", "RelativePath": "api/Categories/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "MyntraClone.API.Controllers.OrdersController", "Method": "PlaceOrder", "RelativePath": "api/Orders", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "dto", "Type": "MyntraClone.API.DTOs.CreateOrderDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "MyntraClone.API.Controllers.OrdersController", "Method": "GetAll", "RelativePath": "api/Orders", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "MyntraClone.API.Controllers.OrdersController", "Method": "GetById", "RelativePath": "api/Orders/{orderId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "orderId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "MyntraClone.API.Controllers.OrdersController", "Method": "GetUserOrders", "RelativePath": "api/Orders/user/{userId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "MyntraClone.API.Controllers.ProductsController", "Method": "GetAll", "RelativePath": "api/Products", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "name", "Type": "System.String", "IsRequired": false}, {"Name": "categoryId", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "page", "Type": "System.Int32", "IsRequired": false}, {"Name": "pageSize", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "MyntraClone.API.Controllers.ProductsController", "Method": "Post", "RelativePath": "api/Products", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "dto", "Type": "MyntraClone.API.DTOs.CreateProductDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "MyntraClone.API.Controllers.ProductsController", "Method": "Get", "RelativePath": "api/Products/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "MyntraClone.API.Controllers.ProductsController", "Method": "Put", "RelativePath": "api/Products/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "dto", "Type": "MyntraClone.API.DTOs.CreateProductDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "MyntraClone.API.Controllers.ProductsController", "Method": "Delete", "RelativePath": "api/Products/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "MyntraClone.API.Controllers.ProductsController", "Method": "UploadImage", "RelativePath": "api/Products/upload", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "file", "Type": "Microsoft.AspNetCore.Http.IFormFile", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "MyntraClone.API.Controllers.UsersController", "Method": "GetAllUsers", "RelativePath": "api/Users", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "MyntraClone.API.Controllers.UsersController", "Method": "CreateUser", "RelativePath": "api/Users", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "dto", "Type": "MyntraClone.API.DTOs.CreateUserDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "MyntraClone.API.Controllers.UsersController", "Method": "GetUser", "RelativePath": "api/Users/<USER>", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "MyntraClone.API.Controllers.UsersController", "Method": "UpdateUser", "RelativePath": "api/Users/<USER>", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "dto", "Type": "MyntraClone.API.DTOs.UpdateUserDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "MyntraClone.API.Controllers.UsersController", "Method": "DeleteUser", "RelativePath": "api/Users/<USER>", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "MyntraClone.API.Controllers.UsersController", "Method": "GetProfile", "RelativePath": "api/Users/<USER>", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "MyntraClone.API.Controllers.WishlistController", "Method": "Get", "RelativePath": "api/Wishlist/{userId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "MyntraClone.API.Controllers.WishlistController", "Method": "AddItem", "RelativePath": "api/Wishlist/items", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "dto", "Type": "MyntraClone.API.DTOs.AddWishlistItemDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "MyntraClone.API.Controllers.WishlistController", "Method": "RemoveItem", "RelativePath": "api/Wishlist/items/{itemId}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "itemId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}]