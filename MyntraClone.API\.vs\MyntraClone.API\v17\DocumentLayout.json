{"Version": 1, "WorkspaceRootPath": "D:\\Moin\\myntra-project\\MyntraClone.API\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{0D2A2511-0AAA-4124-A5DC-F751B1375FCC}|MyntraClone.API.csproj|d:\\moin\\myntra-project\\myntraclone.api\\services\\categoryservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{0D2A2511-0AAA-4124-A5DC-F751B1375FCC}|MyntraClone.API.csproj|solutionrelative:services\\categoryservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{0D2A2511-0AAA-4124-A5DC-F751B1375FCC}|MyntraClone.API.csproj|d:\\moin\\myntra-project\\myntraclone.api\\models\\cart.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{0D2A2511-0AAA-4124-A5DC-F751B1375FCC}|MyntraClone.API.csproj|solutionrelative:models\\cart.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{0D2A2511-0AAA-4124-A5DC-F751B1375FCC}|MyntraClone.API.csproj|d:\\moin\\myntra-project\\myntraclone.api\\dtos\\auth\\logindto.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{0D2A2511-0AAA-4124-A5DC-F751B1375FCC}|MyntraClone.API.csproj|solutionrelative:dtos\\auth\\logindto.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{0D2A2511-0AAA-4124-A5DC-F751B1375FCC}|MyntraClone.API.csproj|d:\\moin\\myntra-project\\myntraclone.api\\dtos\\auth\\authresponsedto.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{0D2A2511-0AAA-4124-A5DC-F751B1375FCC}|MyntraClone.API.csproj|solutionrelative:dtos\\auth\\authresponsedto.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{0D2A2511-0AAA-4124-A5DC-F751B1375FCC}|MyntraClone.API.csproj|d:\\moin\\myntra-project\\myntraclone.api\\dtos\\addwishlistitemdto.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{0D2A2511-0AAA-4124-A5DC-F751B1375FCC}|MyntraClone.API.csproj|solutionrelative:dtos\\addwishlistitemdto.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{0D2A2511-0AAA-4124-A5DC-F751B1375FCC}|MyntraClone.API.csproj|d:\\moin\\myntra-project\\myntraclone.api\\dtos\\cartdto.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{0D2A2511-0AAA-4124-A5DC-F751B1375FCC}|MyntraClone.API.csproj|solutionrelative:dtos\\cartdto.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{0D2A2511-0AAA-4124-A5DC-F751B1375FCC}|MyntraClone.API.csproj|d:\\moin\\myntra-project\\myntraclone.api\\data\\applicationdbcontext.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{0D2A2511-0AAA-4124-A5DC-F751B1375FCC}|MyntraClone.API.csproj|solutionrelative:data\\applicationdbcontext.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{0D2A2511-0AAA-4124-A5DC-F751B1375FCC}|MyntraClone.API.csproj|d:\\moin\\myntra-project\\myntraclone.api\\controllers\\cartcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{0D2A2511-0AAA-4124-A5DC-F751B1375FCC}|MyntraClone.API.csproj|solutionrelative:controllers\\cartcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{0D2A2511-0AAA-4124-A5DC-F751B1375FCC}|MyntraClone.API.csproj|d:\\moin\\myntra-project\\myntraclone.api\\controllers\\authcontroller\\authcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{0D2A2511-0AAA-4124-A5DC-F751B1375FCC}|MyntraClone.API.csproj|solutionrelative:controllers\\authcontroller\\authcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{0D2A2511-0AAA-4124-A5DC-F751B1375FCC}|MyntraClone.API.csproj|d:\\moin\\myntra-project\\myntraclone.api\\myntraclone.api.csproj||{04B8AB82-A572-4FEF-95CE-5222444B6B64}|", "RelativeMoniker": "D:0:0:{0D2A2511-0AAA-4124-A5DC-F751B1375FCC}|MyntraClone.API.csproj|solutionrelative:myntraclone.api.csproj||{04B8AB82-A572-4FEF-95CE-5222444B6B64}|"}, {"AbsoluteMoniker": "D:0:0:{0D2A2511-0AAA-4124-A5DC-F751B1375FCC}|MyntraClone.API.csproj|d:\\moin\\myntra-project\\myntraclone.api\\services\\jwt service\\jwtservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{0D2A2511-0AAA-4124-A5DC-F751B1375FCC}|MyntraClone.API.csproj|solutionrelative:services\\jwt service\\jwtservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{0D2A2511-0AAA-4124-A5DC-F751B1375FCC}|MyntraClone.API.csproj|d:\\moin\\myntra-project\\myntraclone.api\\program.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{0D2A2511-0AAA-4124-A5DC-F751B1375FCC}|MyntraClone.API.csproj|solutionrelative:program.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{0D2A2511-0AAA-4124-A5DC-F751B1375FCC}|MyntraClone.API.csproj|d:\\moin\\myntra-project\\myntraclone.api\\appsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{0D2A2511-0AAA-4124-A5DC-F751B1375FCC}|MyntraClone.API.csproj|solutionrelative:appsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{0D2A2511-0AAA-4124-A5DC-F751B1375FCC}|MyntraClone.API.csproj|d:\\moin\\myntra-project\\myntraclone.api\\properties\\launchsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{0D2A2511-0AAA-4124-A5DC-F751B1375FCC}|MyntraClone.API.csproj|solutionrelative:properties\\launchsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{0D2A2511-0AAA-4124-A5DC-F751B1375FCC}|MyntraClone.API.csproj|d:\\moin\\myntra-project\\myntraclone.api\\controllers\\wishlistcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{0D2A2511-0AAA-4124-A5DC-F751B1375FCC}|MyntraClone.API.csproj|solutionrelative:controllers\\wishlistcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{0D2A2511-0AAA-4124-A5DC-F751B1375FCC}|MyntraClone.API.csproj|d:\\moin\\myntra-project\\myntraclone.api\\controllers\\orderscontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{0D2A2511-0AAA-4124-A5DC-F751B1375FCC}|MyntraClone.API.csproj|solutionrelative:controllers\\orderscontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{0D2A2511-0AAA-4124-A5DC-F751B1375FCC}|MyntraClone.API.csproj|d:\\moin\\myntra-project\\myntraclone.api\\controllers\\userscontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{0D2A2511-0AAA-4124-A5DC-F751B1375FCC}|MyntraClone.API.csproj|solutionrelative:controllers\\userscontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 1, "Children": [{"$type": "Document", "DocumentIndex": 11, "Title": "Program.cs", "DocumentMoniker": "D:\\Moin\\myntra-project\\MyntraClone.API\\Program.cs", "RelativeDocumentMoniker": "Program.cs", "ToolTip": "D:\\Moin\\myntra-project\\MyntraClone.API\\Program.cs", "RelativeToolTip": "Program.cs", "ViewState": "AgIAAIcAAAAAAAAAAAAAALcAAAAbAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-04T11:35:38.365Z", "IsPinned": true}, {"$type": "Document", "DocumentIndex": 0, "Title": "CategoryService.cs", "DocumentMoniker": "D:\\Moin\\myntra-project\\MyntraClone.API\\Services\\CategoryService.cs", "RelativeDocumentMoniker": "Services\\CategoryService.cs", "ToolTip": "D:\\Moin\\myntra-project\\MyntraClone.API\\Services\\CategoryService.cs", "RelativeToolTip": "Services\\CategoryService.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-27T08:47:03.467Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "Cart.cs", "DocumentMoniker": "D:\\Moin\\myntra-project\\MyntraClone.API\\Models\\Cart.cs", "RelativeDocumentMoniker": "Models\\Cart.cs", "ToolTip": "D:\\Moin\\myntra-project\\MyntraClone.API\\Models\\Cart.cs", "RelativeToolTip": "Models\\Cart.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAEAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-27T08:46:50.759Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 2, "Title": "LoginDto.cs", "DocumentMoniker": "D:\\Moin\\myntra-project\\MyntraClone.API\\DTOs\\Auth\\LoginDto.cs", "RelativeDocumentMoniker": "DTOs\\Auth\\LoginDto.cs", "ToolTip": "D:\\Moin\\myntra-project\\MyntraClone.API\\DTOs\\Auth\\LoginDto.cs", "RelativeToolTip": "DTOs\\Auth\\LoginDto.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-27T08:46:46.151Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 3, "Title": "AuthResponseDto.cs", "DocumentMoniker": "D:\\Moin\\myntra-project\\MyntraClone.API\\DTOs\\Auth\\AuthResponseDto.cs", "RelativeDocumentMoniker": "DTOs\\Auth\\AuthResponseDto.cs", "ToolTip": "D:\\Moin\\myntra-project\\MyntraClone.API\\DTOs\\Auth\\AuthResponseDto.cs", "RelativeToolTip": "DTOs\\Auth\\AuthResponseDto.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-27T08:46:44.558Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 4, "Title": "AddWishlistItemDto.cs", "DocumentMoniker": "D:\\Moin\\myntra-project\\MyntraClone.API\\DTOs\\AddWishlistItemDto.cs", "RelativeDocumentMoniker": "DTOs\\AddWishlistItemDto.cs", "ToolTip": "D:\\Moin\\myntra-project\\MyntraClone.API\\DTOs\\AddWishlistItemDto.cs", "RelativeToolTip": "DTOs\\AddWishlistItemDto.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-27T08:46:41.978Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 5, "Title": "CartDto.cs", "DocumentMoniker": "D:\\Moin\\myntra-project\\MyntraClone.API\\DTOs\\CartDto.cs", "RelativeDocumentMoniker": "DTOs\\CartDto.cs", "ToolTip": "D:\\Moin\\myntra-project\\MyntraClone.API\\DTOs\\CartDto.cs", "RelativeToolTip": "DTOs\\CartDto.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-27T08:46:39.818Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 6, "Title": "ApplicationDbContext.cs", "DocumentMoniker": "D:\\Moin\\myntra-project\\MyntraClone.API\\Data\\ApplicationDbContext.cs", "RelativeDocumentMoniker": "Data\\ApplicationDbContext.cs", "ToolTip": "D:\\Moin\\myntra-project\\MyntraClone.API\\Data\\ApplicationDbContext.cs", "RelativeToolTip": "Data\\ApplicationDbContext.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-27T08:46:29.085Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 9, "Title": "MyntraClone.API", "DocumentMoniker": "D:\\Moin\\myntra-project\\MyntraClone.API\\MyntraClone.API.csproj", "RelativeDocumentMoniker": "MyntraClone.API.csproj", "ToolTip": "D:\\Moin\\myntra-project\\MyntraClone.API\\MyntraClone.API.csproj", "RelativeToolTip": "MyntraClone.API.csproj", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000758|", "WhenOpened": "2025-05-27T08:45:01.511Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 8, "Title": "AuthController.cs", "DocumentMoniker": "D:\\Moin\\myntra-project\\MyntraClone.API\\Controllers\\AuthController\\AuthController.cs", "RelativeDocumentMoniker": "Controllers\\AuthController\\AuthController.cs", "ToolTip": "D:\\Moin\\myntra-project\\MyntraClone.API\\Controllers\\AuthController\\AuthController.cs", "RelativeToolTip": "Controllers\\AuthController\\AuthController.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-08T09:05:22.504Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 12, "Title": "appsettings.json", "DocumentMoniker": "D:\\Moin\\myntra-project\\MyntraClone.API\\appsettings.json", "RelativeDocumentMoniker": "appsettings.json", "ToolTip": "D:\\Moin\\myntra-project\\MyntraClone.API\\appsettings.json", "RelativeToolTip": "appsettings.json", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAoAAAAYAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-05-04T11:39:05.967Z"}, {"$type": "Document", "DocumentIndex": 13, "Title": "launchSettings.json", "DocumentMoniker": "D:\\Moin\\myntra-project\\MyntraClone.API\\Properties\\launchSettings.json", "RelativeDocumentMoniker": "Properties\\launchSettings.json", "ToolTip": "D:\\Moin\\myntra-project\\MyntraClone.API\\Properties\\launchSettings.json", "RelativeToolTip": "Properties\\launchSettings.json", "ViewState": "AgIAAAcAAAAAAAAAAAAwwB4AAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-05-04T11:38:29.189Z"}, {"$type": "Document", "DocumentIndex": 14, "Title": "WishlistController.cs", "DocumentMoniker": "D:\\Moin\\myntra-project\\MyntraClone.API\\Controllers\\WishlistController.cs", "RelativeDocumentMoniker": "Controllers\\WishlistController.cs", "ToolTip": "D:\\Moin\\myntra-project\\MyntraClone.API\\Controllers\\WishlistController.cs", "RelativeToolTip": "Controllers\\WishlistController.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAHMAAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-04T11:37:51.801Z"}, {"$type": "Document", "DocumentIndex": 15, "Title": "OrdersController.cs", "DocumentMoniker": "D:\\Moin\\myntra-project\\MyntraClone.API\\Controllers\\OrdersController.cs", "RelativeDocumentMoniker": "Controllers\\OrdersController.cs", "ToolTip": "D:\\Moin\\myntra-project\\MyntraClone.API\\Controllers\\OrdersController.cs", "RelativeToolTip": "Controllers\\OrdersController.cs", "ViewState": "AgIAAIMAAAAAAAAAAAAowJwAAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-04T11:37:27.298Z"}, {"$type": "Document", "DocumentIndex": 10, "Title": "JwtService.cs", "DocumentMoniker": "D:\\Moin\\myntra-project\\MyntraClone.API\\Services\\JWT SERVICE\\JwtService.cs", "RelativeDocumentMoniker": "Services\\JWT SERVICE\\JwtService.cs", "ToolTip": "D:\\Moin\\myntra-project\\MyntraClone.API\\Services\\JWT SERVICE\\JwtService.cs", "RelativeToolTip": "Services\\JWT SERVICE\\JwtService.cs", "ViewState": "AgIAAAMAAAAAAAAAAAAmwD4AAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-04T11:34:39.491Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 7, "Title": "CartController.cs", "DocumentMoniker": "D:\\Moin\\myntra-project\\MyntraClone.API\\Controllers\\CartController.cs", "RelativeDocumentMoniker": "Controllers\\CartController.cs", "ToolTip": "D:\\Moin\\myntra-project\\MyntraClone.API\\Controllers\\CartController.cs", "RelativeToolTip": "Controllers\\CartController.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAIkAAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-04T11:36:47.752Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 16, "Title": "UsersController.cs", "DocumentMoniker": "D:\\Moin\\myntra-project\\MyntraClone.API\\Controllers\\UsersController.cs", "RelativeDocumentMoniker": "Controllers\\UsersController.cs", "ToolTip": "D:\\Moin\\myntra-project\\MyntraClone.API\\Controllers\\UsersController.cs", "RelativeToolTip": "Controllers\\UsersController.cs", "ViewState": "AgIAAFMAAAAAAAAAAAAmwGwAAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-04T11:36:22.257Z"}]}]}]}