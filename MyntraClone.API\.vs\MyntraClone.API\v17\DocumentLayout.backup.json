{"Version": 1, "WorkspaceRootPath": "C:\\Users\\<USER>\\OneDrive\\Desktop\\myntra-project\\MyntraClone.API\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{0D2A2511-0AAA-4124-A5DC-F751B1375FCC}|MyntraClone.API.csproj|c:\\users\\<USER>\\onedrive\\desktop\\myntra-project\\myntraclone.api\\services\\jwt service\\jwtservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{0D2A2511-0AAA-4124-A5DC-F751B1375FCC}|MyntraClone.API.csproj|solutionrelative:services\\jwt service\\jwtservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{0D2A2511-0AAA-4124-A5DC-F751B1375FCC}|MyntraClone.API.csproj|c:\\users\\<USER>\\onedrive\\desktop\\myntra-project\\myntraclone.api\\program.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{0D2A2511-0AAA-4124-A5DC-F751B1375FCC}|MyntraClone.API.csproj|solutionrelative:program.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{0D2A2511-0AAA-4124-A5DC-F751B1375FCC}|MyntraClone.API.csproj|c:\\users\\<USER>\\onedrive\\desktop\\myntra-project\\myntraclone.api\\controllers\\authcontroller\\authcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{0D2A2511-0AAA-4124-A5DC-F751B1375FCC}|MyntraClone.API.csproj|solutionrelative:controllers\\authcontroller\\authcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{0D2A2511-0AAA-4124-A5DC-F751B1375FCC}|MyntraClone.API.csproj|c:\\users\\<USER>\\onedrive\\desktop\\myntra-project\\myntraclone.api\\appsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{0D2A2511-0AAA-4124-A5DC-F751B1375FCC}|MyntraClone.API.csproj|solutionrelative:appsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{0D2A2511-0AAA-4124-A5DC-F751B1375FCC}|MyntraClone.API.csproj|c:\\users\\<USER>\\onedrive\\desktop\\myntra-project\\myntraclone.api\\properties\\launchsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{0D2A2511-0AAA-4124-A5DC-F751B1375FCC}|MyntraClone.API.csproj|solutionrelative:properties\\launchsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{0D2A2511-0AAA-4124-A5DC-F751B1375FCC}|MyntraClone.API.csproj|c:\\users\\<USER>\\onedrive\\desktop\\myntra-project\\myntraclone.api\\controllers\\wishlistcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{0D2A2511-0AAA-4124-A5DC-F751B1375FCC}|MyntraClone.API.csproj|solutionrelative:controllers\\wishlistcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{0D2A2511-0AAA-4124-A5DC-F751B1375FCC}|MyntraClone.API.csproj|c:\\users\\<USER>\\onedrive\\desktop\\myntra-project\\myntraclone.api\\controllers\\orderscontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{0D2A2511-0AAA-4124-A5DC-F751B1375FCC}|MyntraClone.API.csproj|solutionrelative:controllers\\orderscontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{0D2A2511-0AAA-4124-A5DC-F751B1375FCC}|MyntraClone.API.csproj|c:\\users\\<USER>\\onedrive\\desktop\\myntra-project\\myntraclone.api\\controllers\\cartcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{0D2A2511-0AAA-4124-A5DC-F751B1375FCC}|MyntraClone.API.csproj|solutionrelative:controllers\\cartcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{0D2A2511-0AAA-4124-A5DC-F751B1375FCC}|MyntraClone.API.csproj|c:\\users\\<USER>\\onedrive\\desktop\\myntra-project\\myntraclone.api\\controllers\\userscontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{0D2A2511-0AAA-4124-A5DC-F751B1375FCC}|MyntraClone.API.csproj|solutionrelative:controllers\\userscontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 6, "Children": [{"$type": "Document", "DocumentIndex": 1, "Title": "Program.cs", "DocumentMoniker": "C:\\Users\\<USER>\\OneDrive\\Desktop\\myntra-project\\MyntraClone.API\\Program.cs", "RelativeDocumentMoniker": "Program.cs", "ToolTip": "C:\\Users\\<USER>\\OneDrive\\Desktop\\myntra-project\\MyntraClone.API\\Program.cs", "RelativeToolTip": "Program.cs", "ViewState": "AgIAAIcAAAAAAAAAAAAAALcAAAAbAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-04T11:35:38.365Z", "IsPinned": true, "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 2, "Title": "AuthController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\OneDrive\\Desktop\\myntra-project\\MyntraClone.API\\Controllers\\AuthController\\AuthController.cs", "RelativeDocumentMoniker": "Controllers\\AuthController\\AuthController.cs", "ToolTip": "C:\\Users\\<USER>\\OneDrive\\Desktop\\myntra-project\\MyntraClone.API\\Controllers\\AuthController\\AuthController.cs", "RelativeToolTip": "Controllers\\AuthController\\AuthController.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-08T09:05:22.504Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 3, "Title": "appsettings.json", "DocumentMoniker": "C:\\Users\\<USER>\\OneDrive\\Desktop\\myntra-project\\MyntraClone.API\\appsettings.json", "RelativeDocumentMoniker": "appsettings.json", "ToolTip": "C:\\Users\\<USER>\\OneDrive\\Desktop\\myntra-project\\MyntraClone.API\\appsettings.json", "RelativeToolTip": "appsettings.json", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAoAAAAYAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-05-04T11:39:05.967Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 4, "Title": "launchSettings.json", "DocumentMoniker": "C:\\Users\\<USER>\\OneDrive\\Desktop\\myntra-project\\MyntraClone.API\\Properties\\launchSettings.json", "RelativeDocumentMoniker": "Properties\\launchSettings.json", "ToolTip": "C:\\Users\\<USER>\\OneDrive\\Desktop\\myntra-project\\MyntraClone.API\\Properties\\launchSettings.json", "RelativeToolTip": "Properties\\launchSettings.json", "ViewState": "AgIAAAcAAAAAAAAAAAAwwB4AAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-05-04T11:38:29.189Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 5, "Title": "WishlistController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\OneDrive\\Desktop\\myntra-project\\MyntraClone.API\\Controllers\\WishlistController.cs", "RelativeDocumentMoniker": "Controllers\\WishlistController.cs", "ToolTip": "C:\\Users\\<USER>\\OneDrive\\Desktop\\myntra-project\\MyntraClone.API\\Controllers\\WishlistController.cs", "RelativeToolTip": "Controllers\\WishlistController.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAHMAAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-04T11:37:51.801Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 6, "Title": "OrdersController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\OneDrive\\Desktop\\myntra-project\\MyntraClone.API\\Controllers\\OrdersController.cs", "RelativeDocumentMoniker": "Controllers\\OrdersController.cs", "ToolTip": "C:\\Users\\<USER>\\OneDrive\\Desktop\\myntra-project\\MyntraClone.API\\Controllers\\OrdersController.cs", "RelativeToolTip": "Controllers\\OrdersController.cs", "ViewState": "AgIAAIMAAAAAAAAAAAAowJwAAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-04T11:37:27.298Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 0, "Title": "JwtService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\OneDrive\\Desktop\\myntra-project\\MyntraClone.API\\Services\\JWT SERVICE\\JwtService.cs", "RelativeDocumentMoniker": "Services\\JWT SERVICE\\JwtService.cs", "ToolTip": "C:\\Users\\<USER>\\OneDrive\\Desktop\\myntra-project\\MyntraClone.API\\Services\\JWT SERVICE\\JwtService.cs", "RelativeToolTip": "Services\\JWT SERVICE\\JwtService.cs", "ViewState": "AgIAAAMAAAAAAAAAAAAmwD4AAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-04T11:34:39.491Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 7, "Title": "CartController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\OneDrive\\Desktop\\myntra-project\\MyntraClone.API\\Controllers\\CartController.cs", "RelativeDocumentMoniker": "Controllers\\CartController.cs", "ToolTip": "C:\\Users\\<USER>\\OneDrive\\Desktop\\myntra-project\\MyntraClone.API\\Controllers\\CartController.cs", "RelativeToolTip": "Controllers\\CartController.cs", "ViewState": "AgIAAHAAAAAAAAAAAAAmwIkAAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-04T11:36:47.752Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 8, "Title": "UsersController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\OneDrive\\Desktop\\myntra-project\\MyntraClone.API\\Controllers\\UsersController.cs", "RelativeDocumentMoniker": "Controllers\\UsersController.cs", "ToolTip": "C:\\Users\\<USER>\\OneDrive\\Desktop\\myntra-project\\MyntraClone.API\\Controllers\\UsersController.cs", "RelativeToolTip": "Controllers\\UsersController.cs", "ViewState": "AgIAAFMAAAAAAAAAAAAmwGwAAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-04T11:36:22.257Z", "EditorCaption": ""}]}]}]}